'use server'

import { auth } from "@/auth"
import { db } from "@/lib/db"
import { users } from "@/lib/db/schema"
import { eq } from "drizzle-orm"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"

export async function updateName(prev:any ,formData: FormData){
    const session = await auth()
    if (!session) {
        redirect('/sign-in')
    }
    try{
        const name = formData.get("name") as string
        if (!name || name.trim().length === 0) {
            throw new Error("Name is required")
        }

        await db
            .update(users)
            .set({ name: name.trim() })
            .where(eq(users.email, session.user?.email as string))
        revalidatePath('/dashboard/profile')
        return {...prev, name: name.trim(), success: "Name updated successfully"}
    }
    catch (error) {
        return {...prev, error: error instanceof Error ? error.message : "An unexpected error occurred"}
    }
}
