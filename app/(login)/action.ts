'use server'
import { verifyTurnstile } from '@/lib/cf/turnstile'
import { db } from '@/lib/db'
import { users } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { hash } from 'bcryptjs'
const SALT = 10
export async function signUp(prevState: unknown, formData: FormData) {
  // type-casting here for convenience
  // in practice, you should validate your inputs
  const userData = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }
  const token = formData.get('token') as string;
  //check token
  const checkRes = await verifyTurnstile(token);
  if (!checkRes) {
    return { status:'failed', error: new Error('Invalidturnstile token'), email: userData.email, password: userData.password, state:prevState}
  }
  
  const newUser = {
    email: userData.email,
    passwordHash: await hash(userData.password, SALT),
    isActive: true,
    emailVerified: null
  }
  //根据邮箱判断用户是否已经存在
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.email, userData.email));

  if (existingUser) {
    return { status:'failed', error: new Error('User already exists'), email: userData.email, password: userData.password, state:prevState}
  }

  const [user] = await db.insert(users).values(newUser).returning();
  if (!user) {
    return { status:'failed', error: new Error('Failed to create user'), email: userData.email, password: userData.password, state:prevState }
  }
  //set session
  return { status: 'ok', message: 'Sign up successful', user: { id: user.id, email: user.email } }
}
