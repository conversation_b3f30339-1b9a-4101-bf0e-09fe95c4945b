import { NextResponse } from 'next/server'
import { verifyTurnstile } from '@/lib/cf/turnstile'
import { db } from '@/lib/db'
import { messages } from '@/lib/db/schema'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name, email, company, subject, category, message, priority, token } = body

    // 验证 Turnstile token
    const isValid = await verifyTurnstile(token)
    if (!isValid) {
      return NextResponse.json(
        { error: '人机验证失败' },
        { status: 400 }
      )
    }

    // 保存留言到数据库
    const [msg] = await db.insert(messages).values({
      name,
      email,
      company,
      subject,
      category,
      message,
      priority: priority || 'medium',
    }).returning();

    return NextResponse.json(msg)
  } catch (error) {
    return NextResponse.json(
      { error: '提交失败，请稍后重试' },
      { status: 500 }
    )
  }
} 