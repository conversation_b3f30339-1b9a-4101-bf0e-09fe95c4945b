import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { editorMultiImages, editorMultis } from '@/lib/db/schema';
import { eq, desc, count } from 'drizzle-orm';
import { type EditorMultiImage } from '@/lib/db/schema';

// GET 获取用户所有编辑图片记录
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // 获取用户ID
    const userId = session.user.id;

    // 获取用户的所有编辑图片记录，包含编辑任务信息
    const [totalResult] = await db
      .select({ count: count() })
      .from(editorMultiImages)
      .where(eq(editorMultiImages.userId, userId));

    const totalCount = totalResult.count;

    const editImages = await db
      .select({
        id: editorMultiImages.id,
        imageUrl: editorMultiImages.imageUrl,
        tmpUrl: editorMultiImages.tmpUrl,
        createdAt: editorMultiImages.createdAt,
        updatedAt: editorMultiImages.updatedAt,
        editor: {
          id: editorMultis.id,
          userPrompt: editorMultis.userPrompt,
          prompt: editorMultis.prompt,
          status: editorMultis.status,
          createdAt: editorMultis.createdAt,
        }
      })
      .from(editorMultiImages)
      .leftJoin(editorMultis, eq(editorMultiImages.editorId, editorMultis.id))
      .where(eq(editorMultiImages.userId, userId))
      .orderBy(desc(editorMultiImages.createdAt))
      .limit(limit)
      .offset(skip);
    let images: EditorMultiImage[] = []
    if(editImages.length > 0){
      images = editImages.map((editImage) => {
        return {
          id: editImage.id,
          imageUrl:  editImage.imageUrl,
          tmpUrl: editImage.tmpUrl,
          createdAt: editImage.createdAt,
          editorId: editImage.editor.id,
          updatedAt: editImage.updatedAt,
          userId: editImage.userId
        }
      })
    }

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      editImages: images,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit
      }
    });
  } catch (error) {
    console.error('获取用户编辑图片记录失败:', error);
    return NextResponse.json({ error: '获取编辑图片记录失败' }, { status: 500 });
  }
}
