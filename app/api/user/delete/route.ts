import { auth } from "@/auth"
import { NextResponse, type NextRequest } from "next/server"
import { db } from "@/lib/db"
import { users, images, imageGenerations, subscriptions } from "@/lib/db/schema"
import { eq } from "drizzle-orm"

export async function POST(req: NextRequest) {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ status: 'failed', error: 'Unauthorized' })
    }
    try{
        // Delete user images
        await db.delete(images).where(eq(images.userId, session.user.id));

        // Delete user image generations
        await db.delete(imageGenerations).where(eq(imageGenerations.userId, session.user.id));

        // Delete user subscription
        await db.delete(subscriptions).where(eq(subscriptions.userId, session.user.id));

        // Delete user
        const deletedUser = await db.delete(users).where(eq(users.id, session.user.id)).returning();

        if (!deletedUser.length) {
            return NextResponse.json({ status: 'failed', error: 'User not found' })
        }
        return NextResponse.json({ status: 'ok', message: 'User deleted' });
    }catch(error){
      console.error("Error deleting user:", error);
      return NextResponse.json({ status: 'failed', error: 'Internal Server Error' })
    }
}
