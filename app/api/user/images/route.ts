import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { images, imageGenerations } from '@/lib/db/schema';
import { eq, desc, count } from 'drizzle-orm';

// GET 获取用户所有图片记录
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // 获取用户ID
    const userId = session.user.id;

    // 获取用户的所有图片记录，包含生成信息
    const [totalResult] = await db
      .select({ count: count() })
      .from(images)
      .where(eq(images.userId, userId));

    const totalCount = totalResult.count;

    const userImages = await db
      .select({
        id: images.id,
        imageUrl: images.imageUrl,
        thumbnailUrl: images.thumbnailUrl,
        isFavorite: images.isFavorite,
        generation: {
          id: imageGenerations.id,
          userPrompt: imageGenerations.userPrompt,
          prompt: imageGenerations.prompt,
          createdAt: imageGenerations.createdAt,
          modelId: imageGenerations.modelId,
        }
      })
      .from(images)
      .leftJoin(imageGenerations, eq(images.generationId, imageGenerations.id))
      .where(eq(images.userId, userId))
      .orderBy(desc(imageGenerations.createdAt))
      .limit(limit)
      .offset(skip);

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      images: userImages,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit
      }
    });
  } catch (error) {
    console.error('获取用户图片记录失败:', error);
    return NextResponse.json({ error: '获取图片记录失败' }, { status: 500 });
  }
}
