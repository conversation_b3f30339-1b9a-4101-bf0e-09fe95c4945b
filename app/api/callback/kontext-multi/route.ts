'use server'
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { debugs, editorMultis, editorMultiImages, subscriptions } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { uploadImageFromUrlToR2 } from "@/lib/storage";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // 记录调试信息
    const [debug] = await db.insert(debugs).values({
      data: body,
    }).returning();

    const {
      request_id,
      status,
      payload,
      payload_error
    } = body;

    if (status !== "OK") {
      console.error("Multi-image generation failed for task_id:", request_id);
      
      // 查找对应的EditorMulti记录
      const [editorMulti] = await db
        .select()
        .from(editorMultis)
        .where(eq(editorMultis.taskId, request_id));

      if (editorMulti) {
        // 更新状态为失败
        await db
          .update(editorMultis)
          .set({
            status: "FAILED",
            completedAt: new Date()
          })
          .where(eq(editorMultis.id, editorMulti.id));

        // 返还用户积分
        const [currentSubscription] = await db
          .select()
          .from(subscriptions)
          .where(eq(subscriptions.userId, editorMulti.userId));

        if (currentSubscription) {
          await db
            .update(subscriptions)
            .set({
              creditsRemaining: currentSubscription.creditsRemaining + editorMulti.creditsUsed
            })
            .where(eq(subscriptions.userId, editorMulti.userId));
        }
      }

      return NextResponse.json({ error: "Multi-image generation failed" });
    }

    // 查找对应的EditorMulti记录
    const [editorMulti] = await db
      .select()
      .from(editorMultis)
      .where(eq(editorMultis.taskId, request_id));

    if (!editorMulti) {
      console.error("Multi-image generation not found for task_id:", request_id);
      return NextResponse.json({ error: "Multi-image generation not found" }, { status: 404 });
    }

    // 检查是否已经完成
    if (editorMulti.status === 'COMPLETED') {
      console.log('Multi-image generation already completed:', request_id);
      return NextResponse.json({ success: true });
    }

    // 更新EditorMulti记录状态
    await db
      .update(editorMultis)
      .set({
        status: status === "OK" ? "COMPLETED" : "FAILED",
        completedAt: new Date()
      })
      .where(eq(editorMultis.id, editorMulti.id));

    // 如果成功且有图片，创建图片记录
    if (status === "OK" && payload && Array.isArray(payload.images)) {
      await Promise.allSettled(
        payload.images.map(async (img: any, index: number) => {
          const key = `${request_id}-${index}`;
          const imagePath = await uploadImageFromUrlToR2(key, img.url);
          if (imagePath) {
            await db.insert(editorMultiImages).values({
              editorId: editorMulti.id,
              imageUrl: imagePath,
              userId: editorMulti.userId,
              tmpUrl: img.url
            });
          }
        })
      );
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error("Error processing multi-image FAL webhook:", err);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
