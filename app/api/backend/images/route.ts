import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { imageGenerations, images, users } from '@/lib/db/schema';
import { eq, desc, count } from 'drizzle-orm';

export async function GET(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const limit = parseInt(searchParams.get('limit') || '12', 10); // Match frontend

  const skip = (page - 1) * limit;

  try {
    // Get total count
    const [totalResult] = await db.select({ count: count() }).from(imageGenerations);
    const totalGenerations = totalResult.count;

    // Get generations with related data
    const generations = await db
      .select({
        id: imageGenerations.id,
        status: imageGenerations.status,
        completedAt: imageGenerations.completedAt,
        createdAt: imageGenerations.createdAt,
        metadata: imageGenerations.metadata,
        userEmail: users.email,
        modelName: imageGenerations.modelId, // We'll need to join with models table later
        imageUrl: images.imageUrl,
        thumbnailUrl: images.thumbnailUrl,
      })
      .from(imageGenerations)
      .leftJoin(users, eq(imageGenerations.userId, users.id))
      .leftJoin(images, eq(imageGenerations.id, images.generationId))
      .orderBy(desc(imageGenerations.createdAt))
      .limit(limit)
      .offset(skip);

    return NextResponse.json({
      generations,
      totalPages: Math.ceil(totalGenerations / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Failed to fetch generations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch generations' },
      { status: 500 }
    );
  }
}

