import ImageCropper from "@/components/image-cropper";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Free Online Image Editor - Professional Photo Editor | ImageFox",
  description: "Free online image editor with professional editing features like zoom, rotate, and flip. All operations are performed in your browser, ensuring privacy without server uploads.",
  keywords: "Image Crop, Online Image Editor, Image Zoom, Image Rotate, Free Image Tool, Image Processing, Cropper.js",
  openGraph: {
    title: "Free Online Image Editor - Professional Photo Editor | ImageFox",
    description: "Free online image editor with professional editing features like zoom, rotate, and flip. All operations are performed in your browser, ensuring privacy without server uploads.",
    url: 'https://imagefox.art/free-image-edit-online',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox Image Editor Tool',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Free Online Image Editor - Professional Photo Editor | ImageFox",
    description: "Free online image editor with professional editing features like zoom, rotate, and flip. All operations are performed in your browser, ensuring privacy without server uploads.",
    images: ['/logo.png'],
  },
  alternates: {
    canonical: '/free-image-edit-online',
  },
};
export default function ImageCropperPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      <ImageCropper />
    </main>
  )
}
