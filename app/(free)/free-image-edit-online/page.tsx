import ImageCropper from "@/components/image-cropper";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Free Online Image Editor Tool - Professional Image Editor | ImageFox",
  description: "Free online image editor tool with professional editing features including cropping, scaling, rotation, and flipping. All operations are performed in the browser without uploading to servers, protecting your privacy.",
  keywords: "image cropper, online image editor, image scaling, image rotation, free image tools, image processing, Cropper.js",
  openGraph: {
    title: "Free Online Image Editor Tool - Professional Image Editor | ImageFox",
    description: "Free online image editor tool with professional editing features including cropping, scaling, rotation, and flipping. All operations are performed in the browser, protecting your privacy.",
    url: 'https://imagefox.art/free-image-edit-online',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox Image Editor Tool',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Free Online Image Editor Tool - Professional Image Editor | ImageFox",
    description: "Free online image editor tool with professional editing features including cropping, scaling, rotation, and flipping. All operations are performed in the browser, protecting your privacy.",
    images: ['/logo.png'],
  },
  alternates: {
    canonical: '/free-image-edit-online',
  },
};

export default function ImageCropperPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      <div className="mt-20">
      <ImageCropper />
      </div>
    </main>
  )
}
