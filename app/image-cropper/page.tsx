import ImageCropper from "@/components/image-cropper";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "免费在线图片裁剪工具 - 专业图片编辑器 | ImageFox",
  description: "免费的在线图片裁剪工具，支持图片缩放、旋转、翻转等专业编辑功能。所有操作都在浏览器中完成，无需上传到服务器，保护您的隐私。",
  keywords: "图片裁剪, 在线图片编辑, 图片缩放, 图片旋转, 免费图片工具, 图片处理, Cropper.js",
  openGraph: {
    title: "免费在线图片裁剪工具 - 专业图片编辑器 | ImageFox",
    description: "免费的在线图片裁剪工具，支持图片缩放、旋转、翻转等专业编辑功能。所有操作都在浏览器中完成，保护您的隐私。",
    url: 'https://imagefox.art/image-cropper',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox 图片裁剪工具',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "免费在线图片裁剪工具 - 专业图片编辑器 | ImageFox",
    description: "免费的在线图片裁剪工具，支持图片缩放、旋转、翻转等专业编辑功能。所有操作都在浏览器中完成，保护您的隐私。",
    images: ['/logo.png'],
  },
  alternates: {
    canonical: '/image-cropper',
  },
};

export default function ImageCropperPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      <ImageCropper />
    </main>
  )
}
