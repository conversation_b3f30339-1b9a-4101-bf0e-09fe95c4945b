'use client'

import { useEffect, useRef, useState } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Separator } from './ui/separator'
import { toast } from 'sonner'
import {
  Upload,
  Download,
  RotateCw,
  RotateCcw,
  FlipHorizontal,
  FlipVertical,
  ZoomIn,
  ZoomOut,
  Crop,
  RefreshCw,
  ImageIcon
} from 'lucide-react'



// Type declarations for Cropper.js v2.0 Web Components
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'cropper-canvas': any
      'cropper-image': any
      'cropper-selection': any
      'cropper-handle': any
      'cropper-grid': any
      'cropper-crosshair': any
      'cropper-shade': any
      'cropper-viewer': any
    }
  }
}

export default function ImageCropper() {
  const canvasRef = useRef<any>(null)
  const imageRef = useRef<any>(null)
  const selectionRef = useRef<any>(null)
  const viewerRef = useRef<any>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [imageSrc, setImageSrc] = useState<string>('')
  const [croppedImage, setCroppedImage] = useState<string>('')
  const [aspectRatio, setAspectRatio] = useState<string>('free')
  const [quality, setQuality] = useState<number>(0.9)
  const [outputFormat, setOutputFormat] = useState<string>('jpeg')
  const [isProcessing, setIsProcessing] = useState(false)
  const [cropperLoaded, setCropperLoaded] = useState(false)
  const [previewImage, setPreviewImage] = useState<string>('')
  useEffect(() => {
    const loadCropper = async () => {
      try {
        // Import the entire cropperjs package, which automatically defines all custom elements
        // 确保这个导入确实全局注册了自定义元素。
        await import('cropperjs'); 
        setCropperLoaded(true)
        toast.success('Image editor ready!')
      } catch (error) {
        console.error('Failed to load Cropper.js:', error)
        toast.error('Failed to load image editor')
      }
    }

    loadCropper()
  }, [])

  // Live preview handler
  useEffect(() => {
    const canvas = canvasRef.current;
    const selection = selectionRef.current;

    const updatePreview = async () => {
      if (!selection) return;
      try {
        const previewCanvas = await selection.$toCanvas();
        setPreviewImage(previewCanvas.toDataURL());
      } catch (error) {
        // This can fail if the selection is out of bounds, which is normal during interaction.
      }
    };

    if (canvas && selection) {
      // Use a timeout to allow the cropper to load the new image before updating the preview
      const timer = setTimeout(() => {
        updatePreview();
      }, 100);

      canvas.addEventListener('actionend', updatePreview);

      return () => {
        clearTimeout(timer);
        canvas.removeEventListener('actionend', updatePreview);
      };
    }
  }, [imageSrc]);

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image size cannot exceed 10MB')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setImageSrc(result)
      setCroppedImage('')
      setPreviewImage('')
      toast.success('Image uploaded successfully')
    }
    reader.readAsDataURL(file)
  }

  // Crop image
  const handleCrop = async () => {
    if (!selectionRef.current) {
      toast.error('Please make a selection first.');
      return;
    }

    setIsProcessing(true);

    try {
      const croppedCanvas = await selectionRef.current.$toCanvas();
      const croppedDataUrl = croppedCanvas.toDataURL(
        `image/${outputFormat}`,
        quality
      );
      setCroppedImage(croppedDataUrl);
      toast.success('Image cropped successfully');
    } catch (error) {
      console.error('Crop failed:', error);
      toast.error('Crop failed, please try again');
    } finally {
      setIsProcessing(false);
    }
  };

  // Download image
  const handleDownload = () => {
    if (!croppedImage) {
      toast.error('Please crop the image first')
      return
    }

    const link = document.createElement('a')
    link.download = `cropped-image.${outputFormat}`
    link.href = croppedImage
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('Image downloaded successfully')
  }

  // Rotate image
  const handleRotate = (degree: number) => {
    if (imageRef.current && imageRef.current.$rotate) {
      imageRef.current.$rotate(degree)
    }
  }

  // Flip image
  const handleFlip = (direction: 'horizontal' | 'vertical') => {
    if (!imageRef.current || !imageRef.current.$scale) return

    if (direction === 'horizontal') {
      imageRef.current.$scale(-1, 1)
    } else {
      imageRef.current.$scale(1, -1)
    }
  }

  // Zoom image
  const handleZoom = (ratio: number) => {
    if (imageRef.current && imageRef.current.$zoom) {
      imageRef.current.$zoom(ratio)
    }
  }

  // Reset image
  const handleReset = () => {
    if (imageRef.current && imageRef.current.$resetTransform) {
      imageRef.current.$resetTransform()
      toast.success('Image reset successfully')
    }
  }

  // Update aspect ratio
  const updateAspectRatio = (ratio: string) => {
    if (selectionRef.current) {
      if (ratio === 'free') {
        selectionRef.current.removeAttribute('aspect-ratio')
      } else {
        selectionRef.current.setAttribute('aspect-ratio', ratio)
      }
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* Header Section */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Free Image Editor online</h1>
        <p className="text-gray-600">
          Upload images and perform professional cropping, scaling, rotation and other editing operations, all processing is done in the browser, protecting your privacy. no sign up required.
        </p>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload Image
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="w-full max-w-md"
              variant="outline"
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              Select Image File
            </Button>
            <p className="text-sm text-gray-500">
              Supports JPG, PNG, GIF and other formats, maximum 10MB
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Main Editing Area */}
      {imageSrc && cropperLoaded && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Side: Image Editing Area */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crop className="w-5 h-5" />
                  Image Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                  {/* @ts-ignore - Cropper.js v2.0 Web Components */}
                  <cropper-canvas
                    id="cropperCanvas"
                    ref={canvasRef}
                    background
                    style={{
                      width: '100%',
                      height: '600px',
                      display: 'block'
                    }}
                  >
                    {/* @ts-ignore */}
                    <cropper-image
                      ref={imageRef}
                      src={imageSrc}
                      alt="Image to edit"
                      rotatable
                      scalable
                      skewable
                      translatable
                    />
                    {/* @ts-ignore */}
                    <cropper-shade hidden />
                    {/* @ts-ignore */}
                    <cropper-handle action="select" plain />
                    {/* @ts-ignore */}
                    <cropper-selection
                      ref={selectionRef}
                      initial-coverage="0.5"
                      movable
                      resizable
                      aspect-ratio={aspectRatio === 'free' ? undefined : aspectRatio}
                    >
                      {/* @ts-ignore */}
                      <cropper-grid role="grid" bordered covered />
                      {/* @ts-ignore */}
                      <cropper-crosshair centered />
                      {/* @ts-ignore */}
                      <cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)" />
                      {/* @ts-ignore */}
                      <cropper-handle action="n-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="e-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="s-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="w-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="ne-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="nw-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="se-resize" />
                      {/* @ts-ignore */}
                      <cropper-handle action="sw-resize" />
                    {/* @ts-ignore */}
                    </cropper-selection>
                  {/* @ts-ignore */}
                  </cropper-canvas>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Side: Control Panel */}
          <div className="space-y-4">
            {/* Crop Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Crop Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Aspect Ratio</label>
                  <Select
                    value={aspectRatio}
                    onValueChange={(value) => {
                      setAspectRatio(value)
                      updateAspectRatio(value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="free">Free Crop</SelectItem>
                      <SelectItem value="1">1:1 (Square)</SelectItem>
                      <SelectItem value="1.333">4:3</SelectItem>
                      <SelectItem value="0.75">3:4</SelectItem>
                      <SelectItem value="1.777">16:9</SelectItem>
                      <SelectItem value="0.5625">9:16</SelectItem>
                      <SelectItem value="1.5">3:2</SelectItem>
                      <SelectItem value="0.667">2:3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Output Format</label>
                  <Select value={outputFormat} onValueChange={setOutputFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpeg">JPEG</SelectItem>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Image Quality: {Math.round(quality * 100)}%
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={quality}
                    onChange={(e) => setQuality(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <Button
                  onClick={handleCrop}
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? 'Processing...' : 'Crop Image'}
                </Button>
              </CardContent>
            </Card>

            {/* Editing Tools */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Editing Tools</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">

                {/* Rotation Tools */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Rotate</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(-90)}
                      className="flex-1"
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Left
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(90)}
                      className="flex-1"
                    >
                      <RotateCw className="w-4 h-4 mr-1" />
                      Right
                    </Button>
                  </div>
                </div>

                {/* Flip Tools */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Flip</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('horizontal')}
                      className="flex-1"
                    >
                      <FlipHorizontal className="w-4 h-4 mr-1" />
                      Horizontal
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('vertical')}
                      className="flex-1"
                    >
                      <FlipVertical className="w-4 h-4 mr-1" />
                      Vertical
                    </Button>
                  </div>
                </div>

                {/* Zoom Tools */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Zoom</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(0.1)}
                      className="flex-1"
                    >
                      <ZoomIn className="w-4 h-4 mr-1" />
                      Zoom In
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(-0.1)}
                      className="flex-1"
                    >
                      <ZoomOut className="w-4 h-4 mr-1" />
                      Zoom Out
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* Reset Button */}
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Preview and Download Area */}
      {imageSrc && cropperLoaded && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              Live Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col lg:flex-row gap-6 items-center">
              <div className="flex-1">
                <div className="relative bg-gray-100 rounded-lg overflow-hidden p-4">
                  {previewImage ? (
                    <img
                      src={previewImage}
                      alt="Live preview"
                      className="max-w-full h-auto mx-auto"
                      style={{ maxHeight: '300px' }}
                    />
                  ) : (
                    <div 
                      className="flex items-center justify-center bg-gray-200"
                      style={{ height: '300px' }}
                    >
                      <p className="text-gray-500">Live Preview</p>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <Button
                  onClick={handleCrop}
                  disabled={isProcessing}
                  className="min-w-[200px]"
                >
                  {isProcessing ? 'Processing...' : 'Generate Cropped Image'}
                </Button>
                {croppedImage && (
                  <Button
                    onClick={handleDownload}
                    className="min-w-[200px]"
                    variant="outline"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Image
                  </Button>
                )}
                <p className="text-sm text-gray-500 text-center">
                  Format: {outputFormat.toUpperCase()}<br />
                  Quality: {Math.round(quality * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Basic Operations</h4>
              <ul className="space-y-1">
                <li>• Click "Select Image File" to upload an image</li>
                <li>• Drag the crop box to adjust the crop area</li>
                <li>• Use mouse wheel to zoom the image</li>
                <li>• Double-click to toggle move/crop mode</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Advanced Features</h4>
              <ul className="space-y-1">
                <li>• Select different aspect ratios</li>
                <li>• Rotate and flip images</li>
                <li>• Adjust output quality and format</li>
                <li>• All operations are done in the browser</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}