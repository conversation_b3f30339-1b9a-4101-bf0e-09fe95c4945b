'use client'

import { useEffect, useRef, useState } from 'react'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Separator } from './ui/separator'
import { toast } from 'sonner'
import { 
  Upload, 
  Download, 
  RotateCw, 
  RotateCcw, 
  FlipHorizontal, 
  FlipVertical, 
  ZoomIn, 
  ZoomOut, 
  Move, 
  Crop,
  RefreshCw,
  ImageIcon
} from 'lucide-react'

interface CropperData {
  x: number
  y: number
  width: number
  height: number
  rotate: number
  scaleX: number
  scaleY: number
}

export default function ImageCropper() {
  const imageRef = useRef<HTMLImageElement>(null)
  const cropperRef = useRef<Cropper | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [imageSrc, setImageSrc] = useState<string>('')
  const [croppedImage, setCroppedImage] = useState<string>('')
  const [aspectRatio, setAspectRatio] = useState<string>('free')
  const [quality, setQuality] = useState<number>(0.9)
  const [outputFormat, setOutputFormat] = useState<string>('jpeg')
  const [isProcessing, setIsProcessing] = useState(false)

  // 初始化Cropper
  useEffect(() => {
    if (imageSrc && imageRef.current) {
      if (cropperRef.current) {
        cropperRef.current.destroy()
      }
      
      cropperRef.current = new Cropper(imageRef.current, {
        aspectRatio: aspectRatio === 'free' ? NaN : parseFloat(aspectRatio),
        viewMode: 1,
        dragMode: 'move',
        autoCropArea: 0.8,
        restore: false,
        guides: true,
        center: true,
        highlight: false,
        cropBoxMovable: true,
        cropBoxResizable: true,
        toggleDragModeOnDblclick: false,
        background: false,
        responsive: true,
        checkOrientation: false,
      })
    }

    return () => {
      if (cropperRef.current) {
        cropperRef.current.destroy()
        cropperRef.current = null
      }
    }
  }, [imageSrc, aspectRatio])

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件')
      return
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error('图片大小不能超过10MB')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setImageSrc(result)
      setCroppedImage('')
      toast.success('图片上传成功')
    }
    reader.readAsDataURL(file)
  }

  // 裁剪图片
  const handleCrop = () => {
    if (!cropperRef.current) return

    setIsProcessing(true)
    
    try {
      const canvas = cropperRef.current.getCroppedCanvas({
        maxWidth: 4096,
        maxHeight: 4096,
        fillColor: '#fff',
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high',
      })

      const croppedDataUrl = canvas.toDataURL(`image/${outputFormat}`, quality)
      setCroppedImage(croppedDataUrl)
      toast.success('图片裁剪成功')
    } catch (error) {
      console.error('裁剪失败:', error)
      toast.error('裁剪失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  // 下载图片
  const handleDownload = () => {
    if (!croppedImage) {
      toast.error('请先裁剪图片')
      return
    }

    const link = document.createElement('a')
    link.download = `cropped-image.${outputFormat}`
    link.href = croppedImage
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('图片下载成功')
  }

  // 旋转图片
  const handleRotate = (degree: number) => {
    if (cropperRef.current) {
      cropperRef.current.rotate(degree)
    }
  }

  // 翻转图片
  const handleFlip = (direction: 'horizontal' | 'vertical') => {
    if (!cropperRef.current) return
    
    const data = cropperRef.current.getData()
    if (direction === 'horizontal') {
      cropperRef.current.scaleX(-data.scaleX)
    } else {
      cropperRef.current.scaleY(-data.scaleY)
    }
  }

  // 缩放图片
  const handleZoom = (ratio: number) => {
    if (cropperRef.current) {
      cropperRef.current.zoom(ratio)
    }
  }

  // 重置图片
  const handleReset = () => {
    if (cropperRef.current) {
      cropperRef.current.reset()
      toast.success('已重置图片')
    }
  }

  // 移动模式切换
  const handleDragMode = (mode: 'move' | 'crop') => {
    if (cropperRef.current) {
      cropperRef.current.setDragMode(mode)
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* 标题区域 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">专业图片裁剪工具</h1>
        <p className="text-gray-600">
          上传图片，进行专业的裁剪、缩放、旋转等编辑操作，所有处理都在浏览器中完成
        </p>
      </div>

      {/* 上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            上传图片
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="w-full max-w-md"
              variant="outline"
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              选择图片文件
            </Button>
            <p className="text-sm text-gray-500">
              支持 JPG、PNG、GIF 等格式，最大 10MB
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 主编辑区域 */}
      {imageSrc && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：图片编辑区 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crop className="w-5 h-5" />
                  图片编辑
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    ref={imageRef}
                    src={imageSrc}
                    alt="待编辑图片"
                    className="max-w-full h-auto"
                    style={{ maxHeight: '600px' }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：控制面板 */}
          <div className="space-y-4">
            {/* 裁剪比例设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">裁剪设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">裁剪比例</label>
                  <Select value={aspectRatio} onValueChange={setAspectRatio}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="free">自由裁剪</SelectItem>
                      <SelectItem value="1">1:1 (正方形)</SelectItem>
                      <SelectItem value="1.333">4:3</SelectItem>
                      <SelectItem value="0.75">3:4</SelectItem>
                      <SelectItem value="1.777">16:9</SelectItem>
                      <SelectItem value="0.5625">9:16</SelectItem>
                      <SelectItem value="1.5">3:2</SelectItem>
                      <SelectItem value="0.667">2:3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">输出格式</label>
                  <Select value={outputFormat} onValueChange={setOutputFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpeg">JPEG</SelectItem>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    图片质量: {Math.round(quality * 100)}%
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={quality}
                    onChange={(e) => setQuality(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <Button
                  onClick={handleCrop}
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? '处理中...' : '裁剪图片'}
                </Button>
              </CardContent>
            </Card>

            {/* 操作工具 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">编辑工具</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 模式切换 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">操作模式</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDragMode('move')}
                      className="flex-1"
                    >
                      <Move className="w-4 h-4 mr-1" />
                      移动
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDragMode('crop')}
                      className="flex-1"
                    >
                      <Crop className="w-4 h-4 mr-1" />
                      裁剪
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* 旋转工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">旋转</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(-90)}
                      className="flex-1"
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      左转
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(90)}
                      className="flex-1"
                    >
                      <RotateCw className="w-4 h-4 mr-1" />
                      右转
                    </Button>
                  </div>
                </div>

                {/* 翻转工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">翻转</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('horizontal')}
                      className="flex-1"
                    >
                      <FlipHorizontal className="w-4 h-4 mr-1" />
                      水平
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('vertical')}
                      className="flex-1"
                    >
                      <FlipVertical className="w-4 h-4 mr-1" />
                      垂直
                    </Button>
                  </div>
                </div>

                {/* 缩放工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">缩放</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(0.1)}
                      className="flex-1"
                    >
                      <ZoomIn className="w-4 h-4 mr-1" />
                      放大
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(-0.1)}
                      className="flex-1"
                    >
                      <ZoomOut className="w-4 h-4 mr-1" />
                      缩小
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* 重置按钮 */}
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重置
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* 预览和下载区域 */}
      {croppedImage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              裁剪结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col lg:flex-row gap-6 items-center">
              <div className="flex-1">
                <div className="relative bg-gray-100 rounded-lg overflow-hidden p-4">
                  <img
                    src={croppedImage}
                    alt="裁剪后的图片"
                    className="max-w-full h-auto mx-auto"
                    style={{ maxHeight: '400px' }}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <Button
                  onClick={handleDownload}
                  className="min-w-[200px]"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下载图片
                </Button>
                <p className="text-sm text-gray-500 text-center">
                  格式: {outputFormat.toUpperCase()}<br />
                  质量: {Math.round(quality * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">基本操作</h4>
              <ul className="space-y-1">
                <li>• 点击"选择图片文件"上传图片</li>
                <li>• 拖拽裁剪框调整裁剪区域</li>
                <li>• 使用鼠标滚轮缩放图片</li>
                <li>• 双击切换移动/裁剪模式</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">高级功能</h4>
              <ul className="space-y-1">
                <li>• 选择不同的裁剪比例</li>
                <li>• 旋转和翻转图片</li>
                <li>• 调整输出质量和格式</li>
                <li>• 所有操作都在浏览器中完成</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
