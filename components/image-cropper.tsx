'use client'

import { useEffect, useRef, useState } from 'react'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Separator } from './ui/separator'
import { toast } from 'sonner'
import { 
  Upload, 
  Download, 
  RotateCw, 
  RotateCcw, 
  FlipHorizontal, 
  FlipVertical, 
  ZoomIn, 
  ZoomOut, 
  Move, 
  Crop,
  RefreshCw,
  ImageIcon
} from 'lucide-react'

// 声明自定义元素类型
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'cropper-canvas': any
      'cropper-image': any
      'cropper-selection': any
      'cropper-handle': any
      'cropper-grid': any
      'cropper-crosshair': any
      'cropper-shade': any
      'cropper-viewer': any
    }
  }
}

export default function ImageCropper() {
  const canvasRef = useRef<any>(null)
  const imageRef = useRef<any>(null)
  const selectionRef = useRef<any>(null)
  const viewerRef = useRef<any>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [imageSrc, setImageSrc] = useState<string>('')
  const [croppedImage, setCroppedImage] = useState<string>('')
  const [aspectRatio, setAspectRatio] = useState<string>('free')
  const [quality, setQuality] = useState<number>(0.9)
  const [outputFormat, setOutputFormat] = useState<string>('jpeg')
  const [isProcessing, setIsProcessing] = useState(false)
  const [cropperLoaded, setCropperLoaded] = useState(false)

  // 动态加载Cropper.js
  useEffect(() => {
    const loadCropper = async () => {
      try {
        // 导入整个cropperjs包，这会自动定义所有自定义元素
        await import('cropperjs')
        setCropperLoaded(true)
        toast.success('图片编辑器加载成功')
      } catch (error) {
        console.error('Failed to load Cropper.js:', error)
        toast.error('图片编辑器加载失败')
      }
    }

    loadCropper()
  }, [])

  // 设置selection的id以便viewer引用
  useEffect(() => {
    if (selectionRef.current && cropperLoaded) {
      selectionRef.current.id = 'cropperSelection'
    }
  }, [cropperLoaded, imageSrc])

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件')
      return
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error('图片大小不能超过10MB')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setImageSrc(result)
      setCroppedImage('')
      toast.success('图片上传成功')
    }
    reader.readAsDataURL(file)
  }

  // 裁剪图片
  const handleCrop = async () => {
    if (!selectionRef.current || !imageRef.current) {
      toast.error('请先选择裁剪区域')
      return
    }

    setIsProcessing(true)
    
    try {
      // 获取selection的位置和尺寸信息
      const selectionRect = selectionRef.current.getBoundingClientRect()
      const imageRect = imageRef.current.getBoundingClientRect()

      // 创建canvas来绘制裁剪后的图片
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        throw new Error('无法创建Canvas上下文')
      }

      // 获取原始图片
      const img = new Image()
      img.crossOrigin = 'anonymous'

      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = imageSrc
      })

      // 计算裁剪区域相对于图片的比例
      const scaleX = img.naturalWidth / imageRect.width
      const scaleY = img.naturalHeight / imageRect.height

      const cropX = (selectionRect.left - imageRect.left) * scaleX
      const cropY = (selectionRect.top - imageRect.top) * scaleY
      const cropWidth = selectionRect.width * scaleX
      const cropHeight = selectionRect.height * scaleY

      // 设置canvas尺寸
      canvas.width = cropWidth
      canvas.height = cropHeight

      // 绘制裁剪后的图片
      ctx.drawImage(
        img,
        cropX, cropY, cropWidth, cropHeight,
        0, 0, cropWidth, cropHeight
      )

      const croppedDataUrl = canvas.toDataURL(`image/${outputFormat}`, quality)
      setCroppedImage(croppedDataUrl)
      toast.success('图片裁剪成功')
    } catch (error) {
      console.error('裁剪失败:', error)
      toast.error('裁剪失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  // 下载图片
  const handleDownload = () => {
    if (!croppedImage) {
      toast.error('请先裁剪图片')
      return
    }

    const link = document.createElement('a')
    link.download = `cropped-image.${outputFormat}`
    link.href = croppedImage
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('图片下载成功')
  }

  // 旋转图片
  const handleRotate = (degree: number) => {
    if (imageRef.current) {
      imageRef.current.$rotate(degree)
    }
  }

  // 翻转图片
  const handleFlip = (direction: 'horizontal' | 'vertical') => {
    if (!imageRef.current) return
    
    if (direction === 'horizontal') {
      imageRef.current.$scale(-1, 1)
    } else {
      imageRef.current.$scale(1, -1)
    }
  }

  // 缩放图片
  const handleZoom = (ratio: number) => {
    if (imageRef.current) {
      const currentScale = ratio > 0 ? 1 + ratio : 1 / (1 - ratio)
      imageRef.current.$scale(currentScale)
    }
  }

  // 重置图片
  const handleReset = () => {
    if (imageRef.current) {
      imageRef.current.$resetTransform()
      toast.success('已重置图片')
    }
  }

  // 更新裁剪比例
  const updateAspectRatio = (ratio: string) => {
    if (selectionRef.current) {
      if (ratio === 'free') {
        selectionRef.current.removeAttribute('aspect-ratio')
      } else {
        selectionRef.current.setAttribute('aspect-ratio', ratio)
      }
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-4 space-y-6">
      {/* 标题区域 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">专业图片裁剪工具</h1>
        <p className="text-gray-600">
          上传图片，进行专业的裁剪、缩放、旋转等编辑操作，所有处理都在浏览器中完成
        </p>
      </div>

      {/* 上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            上传图片
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="w-full max-w-md"
              variant="outline"
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              选择图片文件
            </Button>
            <p className="text-sm text-gray-500">
              支持 JPG、PNG、GIF 等格式，最大 10MB
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 主编辑区域 */}
      {imageSrc && cropperLoaded && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：图片编辑区 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crop className="w-5 h-5" />
                  图片编辑
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                  <cropper-canvas
                    ref={canvasRef}
                    background
                    style={{
                      width: '100%',
                      height: '600px',
                      display: 'block'
                    }}
                  >
                    <cropper-image
                      ref={imageRef}
                      src={imageSrc}
                      alt="待编辑图片"
                      rotatable
                      scalable
                      skewable
                      translatable
                    />
                    <cropper-shade hidden />
                    <cropper-handle action="select" plain />
                    <cropper-selection
                      ref={selectionRef}
                      initial-coverage="0.5"
                      movable
                      resizable
                      aspect-ratio={aspectRatio === 'free' ? undefined : aspectRatio}
                    >
                      <cropper-grid role="grid" bordered covered />
                      <cropper-crosshair centered />
                      <cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)" />
                      <cropper-handle action="n-resize" />
                      <cropper-handle action="e-resize" />
                      <cropper-handle action="s-resize" />
                      <cropper-handle action="w-resize" />
                      <cropper-handle action="ne-resize" />
                      <cropper-handle action="nw-resize" />
                      <cropper-handle action="se-resize" />
                      <cropper-handle action="sw-resize" />
                    </cropper-selection>
                  </cropper-canvas>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：控制面板 */}
          <div className="space-y-4">
            {/* 裁剪比例设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">裁剪设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">裁剪比例</label>
                  <Select
                    value={aspectRatio}
                    onValueChange={(value) => {
                      setAspectRatio(value)
                      updateAspectRatio(value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="free">自由裁剪</SelectItem>
                      <SelectItem value="1">1:1 (正方形)</SelectItem>
                      <SelectItem value="1.333">4:3</SelectItem>
                      <SelectItem value="0.75">3:4</SelectItem>
                      <SelectItem value="1.777">16:9</SelectItem>
                      <SelectItem value="0.5625">9:16</SelectItem>
                      <SelectItem value="1.5">3:2</SelectItem>
                      <SelectItem value="0.667">2:3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">输出格式</label>
                  <Select value={outputFormat} onValueChange={setOutputFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpeg">JPEG</SelectItem>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    图片质量: {Math.round(quality * 100)}%
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={quality}
                    onChange={(e) => setQuality(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <Button
                  onClick={handleCrop}
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? '处理中...' : '裁剪图片'}
                </Button>
              </CardContent>
            </Card>

            {/* 操作工具 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">编辑工具</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">

                {/* 旋转工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">旋转</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(-90)}
                      className="flex-1"
                    >
                      <RotateCcw className="w-4 h-4 mr-1" />
                      左转
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRotate(90)}
                      className="flex-1"
                    >
                      <RotateCw className="w-4 h-4 mr-1" />
                      右转
                    </Button>
                  </div>
                </div>

                {/* 翻转工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">翻转</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('horizontal')}
                      className="flex-1"
                    >
                      <FlipHorizontal className="w-4 h-4 mr-1" />
                      水平
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFlip('vertical')}
                      className="flex-1"
                    >
                      <FlipVertical className="w-4 h-4 mr-1" />
                      垂直
                    </Button>
                  </div>
                </div>

                {/* 缩放工具 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">缩放</label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(0.1)}
                      className="flex-1"
                    >
                      <ZoomIn className="w-4 h-4 mr-1" />
                      放大
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(-0.1)}
                      className="flex-1"
                    >
                      <ZoomOut className="w-4 h-4 mr-1" />
                      缩小
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* 重置按钮 */}
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重置
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* 预览和下载区域 */}
      {imageSrc && cropperLoaded && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              实时预览
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col lg:flex-row gap-6 items-center">
              <div className="flex-1">
                <div className="relative bg-gray-100 rounded-lg overflow-hidden p-4">
                  <cropper-viewer
                    ref={viewerRef}
                    selection="#cropperSelection"
                    style={{
                      width: '100%',
                      height: '300px',
                      display: 'block',
                      border: '1px solid #ddd'
                    }}
                  />
                  {croppedImage && (
                    <img
                      src={croppedImage}
                      alt="裁剪后的图片"
                      className="max-w-full h-auto mx-auto mt-4"
                      style={{ maxHeight: '300px' }}
                    />
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <Button
                  onClick={handleCrop}
                  disabled={isProcessing}
                  className="min-w-[200px]"
                >
                  {isProcessing ? '处理中...' : '生成裁剪图片'}
                </Button>
                {croppedImage && (
                  <Button
                    onClick={handleDownload}
                    className="min-w-[200px]"
                    variant="outline"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载图片
                  </Button>
                )}
                <p className="text-sm text-gray-500 text-center">
                  格式: {outputFormat.toUpperCase()}<br />
                  质量: {Math.round(quality * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}