"use client"

import { useState } from "react"
import { <PERSON>u, User, X, LogOut, Image as Image1, WandSpark<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { signOut, useSession } from "next-auth/react"
import UserButton from "./user-button"
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
  NavigationMenuTrigger,
  NavigationMenuContent,
} from "@/components/ui/navigation-menu"
import {
  Sheet,  
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import React from "react";
import Image from "next/image";

interface NavLinkItem {
  href: string;
  text: string;
  subLinks?: NavLinkItem[];
}

const navLinks: NavLinkItem[] = [
  { href: "/free-image-generator", text: "Free Image Generator" },
  { href: "/photo-editor", text: "Photo Editor" },
  { href: "#", text: "Free Tools", subLinks:[
    { href: "/background-remover", text: "Background Remover" },
    { href: "/free-image-edit-online", text: "Free Image Edit Online" },
  ]},
  {
    href: "/#features", text: "Features"},
  { href: "/#cases", text: "Cases" },
  { href: "/#pricing", text: "Pricing" },
  { href: "/#faq", text: "FAQ" },
];

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const session = useSession()

  return (
    <nav className="sticky top-0 w-full bg-white/95 backdrop-blur-sm border-b border-[#E0E0E0] z-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center h-16 px-4">
          {/* Logo */}
          <div className="flex items-center gap-4">
            <Image src="/logo.png" alt="ImageFox" width={60} height={60} />
            <Link href="/">
              <span className="text-xl font-semibold">ImageFox</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-4 relative">
          <NavigationMenu viewport={false}>
            <NavigationMenuList>
              {navLinks.map((link) => (
                <NavigationMenuItem key={link.href + link.text}>
                  {link.subLinks ? (
                    <>
                      <NavigationMenuTrigger>{link.text}</NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid w-[200px] gap-3 p-4 md:w-[250px] ">
                          {link.subLinks.map((subLink) => (
                            <ListItem key={subLink.href + subLink.text} href={subLink.href} title={subLink.text} />
                          ))}
                           {/* You can add more complex items here if needed */}
                        </ul>
                      </NavigationMenuContent>
                    </>
                  ) : (
                    
                      <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
                        <Link href={link.href} passHref>
                        {link.text}
                        </Link>
                      </NavigationMenuLink>
                  )}
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
          </div>
          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <UserButton />
          </div>

          {/* Mobile menu button */}
          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="outline" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full max-w-xs">
              <SheetHeader>
                <SheetTitle>
                  <Link href="/" onClick={() => setMobileMenuOpen(false)}>
                    <span className="text-xl font-semibold">Image</span>
                    <span className="text-xl font-semibold text-[#f5a]">Fox</span>
                  </Link>
                </SheetTitle>
              </SheetHeader>
              <div className="mt-8 flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <React.Fragment key={link.href + link.text}>
                    <Link
                      href={link.href}
                      onClick={() => {
                        // If it has sublinks and you want the main link to be non-clickable or act as a toggle in mobile, adjust logic here.
                        // For now, all main links are clickable.
                        setMobileMenuOpen(false);
                      }}
                      className="text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-2 px-3 rounded-lg transition-all"
                    >
                      {link.text}
                    </Link>
                    {link.subLinks && (
                      <div className="flex flex-col space-y-2 pl-6">
                        {link.subLinks.map((subLink) => (
                          <Link key={subLink.href + subLink.text} href={subLink.href} onClick={() => setMobileMenuOpen(false)} className="text-md font-medium text-gray-700 hover:text-black hover:bg-gray-50 py-1 px-3 rounded-lg transition-all">
                            {subLink.text}
                          </Link>
                        ))}
                      </div>
                    )}
                  </React.Fragment>
                ))}
                <hr />
                {session.data?.user ? (
                  <>
                    <Link
                      href="/profile"
                      onClick={() => setMobileMenuOpen(false)}
                      className="flex items-center text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-2 px-3 rounded-lg transition-all"
                    >
                      <User size={20} className="mr-3" />
                      Profile
                    </Link>
                    <Link
                      href="/generate-history"
                      onClick={() => setMobileMenuOpen(false)}
                      className="flex items-center text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-2 px-3 rounded-lg transition-all"
                    >
                      <Image1 size={20} className="mr-3" />
                      Generate History
                    </Link>
                    <Link
                      href="/edit-history"
                      onClick={() => setMobileMenuOpen(false)}
                      className="flex items-center text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-2 px-3 rounded-lg transition-all"
                    >
                      <WandSparkles size={20} className="mr-3" />
                      Edit History
                    </Link>
                    <button
                      onClick={() => {
                        signOut({ redirect: true, callbackUrl: "/" });
                        setMobileMenuOpen(false);
                      }}
                      className="flex items-center w-full text-left text-lg font-medium text-red-600 hover:text-red-700 hover:bg-white py-2 px-3 rounded-lg transition-all"
                    >
                      <LogOut size={20} className="mr-3" />
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/sign-in"
                      onClick={() => setMobileMenuOpen(false)}
                      className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-2 px-3 rounded-lg transition-all text-center"
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/sign-up"
                      onClick={() => setMobileMenuOpen(false)}
                      className="block"
                    >
                      <Button className="w-full bg-[#121212] text-white hover:bg-[#333333] py-2 text-lg font-medium rounded-lg">
                        Sign Up
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  )
}

// Helper component for NavigationMenuContent, can be customized further
const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          ref={ref}
          href={props.href as string}
          className={`block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground ${className}`}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          {children && <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</p>}
        </Link>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
