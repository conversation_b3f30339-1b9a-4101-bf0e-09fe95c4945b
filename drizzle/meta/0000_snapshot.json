{"id": "e103be66-08c4-421a-b2f2-3127652d2936", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.Account": {"name": "Account", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"Account_provider_providerAccountId_pk": {"name": "Account_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Authenticator": {"name": "Authenticator", "schema": "", "columns": {"credentialID": {"name": "credentialID", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "credentialPublicKey": {"name": "credentialPublicKey", "type": "text", "primaryKey": false, "notNull": true}, "counter": {"name": "counter", "type": "integer", "primaryKey": false, "notNull": true}, "credentialDeviceType": {"name": "credentialDeviceType", "type": "text", "primaryKey": false, "notNull": true}, "credentialBackedUp": {"name": "credentialBackedUp", "type": "boolean", "primaryKey": false, "notNull": true}, "transports": {"name": "transports", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"Authenticator_userId_credentialID_pk": {"name": "Authenticator_userId_credentialID_pk", "columns": ["userId", "credentialID"]}}, "uniqueConstraints": {"Authenticator_credentialID_unique": {"name": "Authenticator_credentialID_unique", "nullsNotDistinct": false, "columns": ["credentialID"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Debug": {"name": "Debug", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "Debug_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.EditorMultiImage": {"name": "EditorMultiImage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "editorId": {"name": "editorId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "tmpUrl": {"name": "tmpUrl", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.EditorMulti": {"name": "Editor<PERSON>ulti", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "taskId": {"name": "taskId", "type": "text", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "json", "primaryKey": false, "notNull": false}, "userPrompt": {"name": "userPrompt", "type": "text", "primaryKey": false, "notNull": false}, "imageUrls": {"name": "imageUrls", "type": "json", "primaryKey": false, "notNull": true}, "creditsUsed": {"name": "creditsUsed", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "GenerationStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ImageGeneration": {"name": "ImageGeneration", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "userPrompt": {"name": "userPrompt", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "negativePrompt": {"name": "negativePrompt", "type": "text", "primaryKey": false, "notNull": false}, "taskId": {"name": "taskId", "type": "text", "primaryKey": false, "notNull": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "json", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "GenerationStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "creditsUsed": {"name": "creditsUsed", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Image": {"name": "Image", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "generationId": {"name": "generationId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "thumbnailUrl": {"name": "thumbnailUrl", "type": "text", "primaryKey": false, "notNull": false}, "storagePath": {"name": "storagePath", "type": "text", "primaryKey": false, "notNull": false}, "isFavorite": {"name": "isFavorite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "default": "'medium'"}, "isRead": {"name": "isRead", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Model": {"name": "Model", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "modelName": {"name": "modelName", "type": "text", "primaryKey": false, "notNull": true}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "ModelCategory", "typeSchema": "public", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isAvailable": {"name": "isAvailable", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "creditCost": {"name": "creditCost", "type": "integer", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "json", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "ModelType", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STANDARD'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Model_modelId_unique": {"name": "Model_modelId_unique", "nullsNotDistinct": false, "columns": ["modelId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Session": {"name": "Session", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "SubscriptionType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "SubscriptionStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'INACTIVE'"}, "creditsGrantedPerMonth": {"name": "creditsGrantedPerMonth", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "creditsRemaining": {"name": "creditsRemaining", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "timestamp", "primaryKey": false, "notNull": false}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "timestamp", "primaryKey": false, "notNull": false}, "trialEndsAt": {"name": "trialEndsAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "stripeSubscriptionId": {"name": "stripeSubscriptionId", "type": "text", "primaryKey": false, "notNull": false}, "stripeCustomerId": {"name": "stripeCustomerId", "type": "text", "primaryKey": false, "notNull": false}, "stripePaymentMethodId": {"name": "stripePaymentMethodId", "type": "text", "primaryKey": false, "notNull": false}, "stripePriceId": {"name": "stripePriceId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Subscription_userId_unique": {"name": "Subscription_userId_unique", "nullsNotDistinct": false, "columns": ["userId"]}, "Subscription_stripeSubscriptionId_unique": {"name": "Subscription_stripeSubscriptionId_unique", "nullsNotDistinct": false, "columns": ["stripeSubscriptionId"]}, "Subscription_stripeCustomerId_unique": {"name": "Subscription_stripeCustomerId_unique", "nullsNotDistinct": false, "columns": ["stripeCustomerId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "passwordHash": {"name": "passwordHash", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"User_email_unique": {"name": "User_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.VerificationToken": {"name": "VerificationToken", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"VerificationToken_identifier_token_pk": {"name": "VerificationToken_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.GenerationStatus": {"name": "GenerationStatus", "schema": "public", "values": ["INIT", "PENDING", "COMPLETED", "FAILED"]}, "public.ModelCategory": {"name": "ModelCategory", "schema": "public", "values": ["TEXT_TO_IMAGE", "IMAGE_TO_IMAGE", "IMAGE_EDIT", "IMAGE_ENHANCEMENT", "IMAGE_TO_TEXT", "TEXT_TO_VIDEO", "IMAGE_TO_VIDEO", "VIDEO_TO_VIDEO"]}, "public.ModelType": {"name": "ModelType", "schema": "public", "values": ["STANDARD", "PREMIUM"]}, "public.SubscriptionStatus": {"name": "SubscriptionStatus", "schema": "public", "values": ["ACTIVE", "CANCELED", "INACTIVE", "PAST_DUE", "TRIALING"]}, "public.SubscriptionType": {"name": "SubscriptionType", "schema": "public", "values": ["FREE", "PREMIUM"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}