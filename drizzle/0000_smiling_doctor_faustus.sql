CREATE TYPE "public"."GenerationStatus" AS ENUM('INIT', 'PENDING', 'COMPLETED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."ModelCategory" AS ENUM('TEXT_TO_IMAGE', 'IMAGE_TO_IMAGE', 'IMAGE_EDIT', 'IMAGE_ENHANCEMENT', 'IMAGE_TO_TEXT', 'TEXT_TO_VIDEO', 'IMAGE_TO_VIDEO', 'VIDEO_TO_VIDEO');--> statement-breakpoint
CREATE TYPE "public"."ModelType" AS ENUM('STANDARD', 'PREMIUM');--> statement-breakpoint
CREATE TYPE "public"."SubscriptionStatus" AS ENUM('ACTIVE', 'CANCELED', 'INACTIVE', 'PAST_DUE', 'TRIALING');--> statement-breakpoint
CREATE TYPE "public"."SubscriptionType" AS ENUM('FREE', 'PREMIUM');--> statement-breakpoint
CREATE TABLE "Account" (
	"userId" text NOT NULL,
	"type" text NOT NULL,
	"provider" text NOT NULL,
	"providerAccountId" text NOT NULL,
	"refresh_token" text,
	"access_token" text,
	"expires_at" integer,
	"token_type" text,
	"scope" text,
	"id_token" text,
	"session_state" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Account_provider_providerAccountId_pk" PRIMARY KEY("provider","providerAccountId")
);
--> statement-breakpoint
CREATE TABLE "Authenticator" (
	"credentialID" text NOT NULL,
	"userId" text NOT NULL,
	"providerAccountId" text NOT NULL,
	"credentialPublicKey" text NOT NULL,
	"counter" integer NOT NULL,
	"credentialDeviceType" text NOT NULL,
	"credentialBackedUp" boolean NOT NULL,
	"transports" text,
	CONSTRAINT "Authenticator_userId_credentialID_pk" PRIMARY KEY("userId","credentialID"),
	CONSTRAINT "Authenticator_credentialID_unique" UNIQUE("credentialID")
);
--> statement-breakpoint
CREATE TABLE "Debug" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "Debug_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"data" json NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "EditorMultiImage" (
	"id" text PRIMARY KEY NOT NULL,
	"editorId" text NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"tmpUrl" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "EditorMulti" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"taskId" text,
	"prompt" text NOT NULL,
	"parameters" json,
	"userPrompt" text,
	"imageUrls" json NOT NULL,
	"creditsUsed" integer NOT NULL,
	"metadata" json,
	"status" "GenerationStatus" DEFAULT 'PENDING' NOT NULL,
	"completedAt" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ImageGeneration" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"userPrompt" text NOT NULL,
	"prompt" text NOT NULL,
	"negativePrompt" text,
	"taskId" text,
	"modelId" text NOT NULL,
	"parameters" json NOT NULL,
	"status" "GenerationStatus" DEFAULT 'PENDING' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"completedAt" timestamp,
	"creditsUsed" integer NOT NULL,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "Image" (
	"id" text PRIMARY KEY NOT NULL,
	"generationId" text NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"thumbnailUrl" text,
	"storagePath" text,
	"isFavorite" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Message" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"company" text,
	"subject" text NOT NULL,
	"category" text NOT NULL,
	"message" text NOT NULL,
	"priority" text DEFAULT 'medium' NOT NULL,
	"isRead" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Model" (
	"id" text PRIMARY KEY NOT NULL,
	"modelName" text NOT NULL,
	"modelId" text NOT NULL,
	"category" "ModelCategory" NOT NULL,
	"provider" text NOT NULL,
	"description" text,
	"isAvailable" boolean DEFAULT true NOT NULL,
	"creditCost" integer NOT NULL,
	"parameters" json,
	"type" "ModelType" DEFAULT 'STANDARD' NOT NULL,
	CONSTRAINT "Model_modelId_unique" UNIQUE("modelId")
);
--> statement-breakpoint
CREATE TABLE "Session" (
	"sessionToken" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"expires" timestamp NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Subscription" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"type" "SubscriptionType" NOT NULL,
	"status" "SubscriptionStatus" DEFAULT 'INACTIVE' NOT NULL,
	"creditsGrantedPerMonth" integer DEFAULT 0 NOT NULL,
	"creditsRemaining" integer DEFAULT 0 NOT NULL,
	"currentPeriodStart" timestamp,
	"currentPeriodEnd" timestamp,
	"trialEndsAt" timestamp,
	"stripeSubscriptionId" text,
	"stripeCustomerId" text,
	"stripePaymentMethodId" text,
	"stripePriceId" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Subscription_userId_unique" UNIQUE("userId"),
	CONSTRAINT "Subscription_stripeSubscriptionId_unique" UNIQUE("stripeSubscriptionId"),
	CONSTRAINT "Subscription_stripeCustomerId_unique" UNIQUE("stripeCustomerId")
);
--> statement-breakpoint
CREATE TABLE "User" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text,
	"email" text NOT NULL,
	"passwordHash" text,
	"emailVerified" timestamp,
	"image" text,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "User_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "VerificationToken" (
	"identifier" text NOT NULL,
	"token" text NOT NULL,
	"expires" timestamp NOT NULL,
	CONSTRAINT "VerificationToken_identifier_token_pk" PRIMARY KEY("identifier","token")
);
