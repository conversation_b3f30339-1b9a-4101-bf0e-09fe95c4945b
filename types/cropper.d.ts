// Type declarations for Cropper.js v2.0 Web Components
declare namespace JSX {
  interface IntrinsicElements {
    'cropper-canvas': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      background?: boolean;
      disabled?: boolean;
      ref?: React.Ref<any>;
    };
    'cropper-image': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      src?: string;
      alt?: string;
      rotatable?: boolean;
      scalable?: boolean;
      skewable?: boolean;
      translatable?: boolean;
      slottable?: boolean;
      ref?: React.Ref<any>;
    };
    'cropper-selection': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      'initial-coverage'?: string;
      'initial-aspect-ratio'?: string;
      'aspect-ratio'?: string;
      x?: string;
      y?: string;
      width?: string;
      height?: string;
      movable?: boolean;
      resizable?: boolean;
      zoomable?: boolean;
      outlined?: boolean;
      multiple?: boolean;
      keyboard?: boolean;
      dynamic?: boolean;
      hidden?: boolean;
      ref?: React.Ref<any>;
    };
    'cropper-handle': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      action?: string;
      plain?: boolean;
      'theme-color'?: string;
      slottable?: boolean;
    };
    'cropper-grid': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      rows?: string;
      columns?: string;
      'theme-color'?: string;
      bordered?: boolean;
      covered?: boolean;
      slottable?: boolean;
    };
    'cropper-crosshair': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      'theme-color'?: string;
      centered?: boolean;
      slottable?: boolean;
    };
    'cropper-shade': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      x?: string;
      y?: string;
      width?: string;
      height?: string;
      hidden?: boolean;
    };
    'cropper-viewer': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
      selection?: string;
      slottable?: boolean;
      ref?: React.Ref<any>;
    };
  }
}

// Extend the global HTMLElement interface for custom methods
declare global {
  interface HTMLElement {
    $rotate?: (degree: number) => void;
    $scale?: (scaleX: number, scaleY?: number) => void;
    $zoom?: (ratio: number, originX?: number, originY?: number) => void;
    $resetTransform?: () => void;
    $ready?: (callback: (element: HTMLElement) => void) => void;
    $emit?: (eventName: string, detail?: any) => void;
    $addStyles?: (styles: string) => void;
    $toCanvas?: (options?: any) => Promise<HTMLCanvasElement>;
  }
}

export {};
