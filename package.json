{"name": "saas-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@aws-sdk/client-ses": "^3.826.0", "@marsidev/react-turnstile": "^1.1.0", "@next/third-parties": "^15.3.3", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-table": "^8.21.3", "@types/cropperjs": "^1.3.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "^2.0.0", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "framer-motion": "^12.12.1", "jose": "^6.0.11", "lucide-react": "^0.483.0", "next": "15.3.2", "next-auth": "5.0.0-beta.28", "next-themes": "^0.4.6", "openai": "^4.98.0", "pg": "^8.16.3", "react": "19.1.0", "react-compare-image": "^3.5.4", "react-dom": "19.1.0", "react-resizable-panels": "^3.0.2", "react-turnstile": "^1.1.4", "replicate": "^1.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.46", "@types/pg": "^8.15.4", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.7.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "pnpm": {"overrides": {"@types/react": "19.1.6", "@types/react-dom": "19.1.5"}}, "prisma": {"seed": "pnpm dlx tsx prisma/seed.ts"}}