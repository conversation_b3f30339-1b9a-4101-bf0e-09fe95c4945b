import { pgTable, text, timestamp, boolean, integer, json, pgEnum, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const subscriptionStatusEnum = pgEnum('SubscriptionStatus', [
  'ACTIVE',
  'CANCELED',
  'INACTIVE',
  'PAST_DUE',
  'TRIALING'
]);

export const subscriptionTypeEnum = pgEnum('SubscriptionType', [
  'FREE',
  'PREMIUM'
]);

export const generationStatusEnum = pgEnum('GenerationStatus', [
  'INIT',
  'PENDING',
  'COMPLETED',
  'FAILED'
]);

export const modelCategoryEnum = pgEnum('ModelCategory', [
  'TEXT_TO_IMAGE',
  'IMAGE_TO_IMAGE',
  'IMAGE_EDIT',
  'IMAGE_ENHANCEMENT',
  'IMAGE_TO_TEXT',
  'TEXT_TO_VIDEO',
  'IMAGE_TO_VIDEO',
  'VIDEO_TO_VIDEO'
]);

export const modelTypeEnum = pgEnum('ModelType', [
  'STANDARD',
  'PREMIUM'
]);

// Tables
export const users = pgTable('User', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name'),
  email: text('email').notNull().unique(),
  passwordHash: text('passwordHash'),
  emailVerified: timestamp('emailVerified'),
  image: text('image'),
  isActive: boolean('isActive').default(true).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const accounts = pgTable('Account', {
  userId: text('userId').notNull(),
  type: text('type').notNull(),
  provider: text('provider').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: integer('expires_at'),
  token_type: text('token_type'),
  scope: text('scope'),
  id_token: text('id_token'),
  session_state: text('session_state'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
}, (account) => [
  primaryKey({ columns: [account.provider, account.providerAccountId] }),
]);

export const sessions = pgTable('Session', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: text('userId').notNull(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const verificationTokens = pgTable('VerificationToken', {
  identifier: text('identifier').notNull(),
  token: text('token').notNull(),
  expires: timestamp('expires').notNull(),
}, (vt) => [
  primaryKey({ columns: [vt.identifier, vt.token] }),
]);

export const authenticators = pgTable('Authenticator', {
  credentialID: text('credentialID').notNull().unique(),
  userId: text('userId').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  credentialPublicKey: text('credentialPublicKey').notNull(),
  counter: integer('counter').notNull(),
  credentialDeviceType: text('credentialDeviceType').notNull(),
  credentialBackedUp: boolean('credentialBackedUp').notNull(),
  transports: text('transports'),
}, (authenticator) => [
  primaryKey({ columns: [authenticator.userId, authenticator.credentialID] }),
]);

export const subscriptions = pgTable('Subscription', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull().unique(),
  type: subscriptionTypeEnum('type').notNull(),
  status: subscriptionStatusEnum('status').default('INACTIVE').notNull(),
  creditsGrantedPerMonth: integer('creditsGrantedPerMonth').default(0).notNull(),
  creditsRemaining: integer('creditsRemaining').default(0).notNull(),
  currentPeriodStart: timestamp('currentPeriodStart'),
  currentPeriodEnd: timestamp('currentPeriodEnd'),
  trialEndsAt: timestamp('trialEndsAt'),
  stripeSubscriptionId: text('stripeSubscriptionId').unique(),
  stripeCustomerId: text('stripeCustomerId').unique(),
  stripePaymentMethodId: text('stripePaymentMethodId'),
  stripePriceId: text('stripePriceId'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const models = pgTable('Model', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  modelName: text('modelName').notNull(),
  modelId: text('modelId').notNull().unique(),
  category: modelCategoryEnum('category').notNull(),
  provider: text('provider').notNull(),
  description: text('description'),
  isAvailable: boolean('isAvailable').default(true).notNull(),
  creditCost: integer('creditCost').notNull(),
  parameters: json('parameters'),
  type: modelTypeEnum('type').default('STANDARD').notNull(),
});

export const imageGenerations = pgTable('ImageGeneration', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull(),
  userPrompt: text('userPrompt').notNull(),
  prompt: text('prompt').notNull(),
  negativePrompt: text('negativePrompt'),
  taskId: text('taskId'),
  modelId: text('modelId').notNull(),
  parameters: json('parameters').notNull(),
  status: generationStatusEnum('status').default('PENDING').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  completedAt: timestamp('completedAt'),
  creditsUsed: integer('creditsUsed').notNull(),
  metadata: json('metadata'),
});

export const images = pgTable('Image', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  generationId: text('generationId').notNull(),
  userId: text('userId').notNull(),
  imageUrl: text('imageUrl').notNull(),
  thumbnailUrl: text('thumbnailUrl'),
  storagePath: text('storagePath'),
  isFavorite: boolean('isFavorite').default(false).notNull(),
});

export const editorMultis = pgTable('EditorMulti', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull(),
  taskId: text('taskId'),
  prompt: text('prompt').notNull(),
  parameters: json('parameters'),
  userPrompt: text('userPrompt'),
  imageUrls: json('imageUrls').$type<string[]>().notNull(),
  creditsUsed: integer('creditsUsed').notNull(),
  metadata: json('metadata'),
  status: generationStatusEnum('status').default('PENDING').notNull(),
  completedAt: timestamp('completedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const editorMultiImages = pgTable('EditorMultiImage', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  editorId: text('editorId').notNull(),
  userId: text('userId').notNull(),
  imageUrl: text('imageUrl').notNull(),
  tmpUrl: text('tmpUrl'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const debugs = pgTable('Debug', {
  id: integer('id').primaryKey().generatedAlwaysAsIdentity(),
  data: json('data').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

export const messages = pgTable('Message', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name').notNull(),
  email: text('email').notNull(),
  company: text('company'),
  subject: text('subject').notNull(),
  category: text('category').notNull(),
  message: text('message').notNull(),
  priority: text('priority').default('medium').notNull(),
  isRead: boolean('isRead').default(false).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  authenticators: many(authenticators),
  subscription: one(subscriptions),
  imageGenerations: many(imageGenerations),
  images: many(images),
  editorMultis: many(editorMultis),
  editorMultiImages: many(editorMultiImages),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const authenticatorsRelations = relations(authenticators, ({ one }) => ({
  user: one(users, {
    fields: [authenticators.userId],
    references: [users.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  user: one(users, {
    fields: [subscriptions.userId],
    references: [users.id],
  }),
}));

export const imageGenerationsRelations = relations(imageGenerations, ({ one, many }) => ({
  user: one(users, {
    fields: [imageGenerations.userId],
    references: [users.id],
  }),
  model: one(models, {
    fields: [imageGenerations.modelId],
    references: [models.id],
  }),
  images: many(images),
}));

export const imagesRelations = relations(images, ({ one }) => ({
  user: one(users, {
    fields: [images.userId],
    references: [users.id],
  }),
  generation: one(imageGenerations, {
    fields: [images.generationId],
    references: [imageGenerations.id],
  }),
}));

export const modelsRelations = relations(models, ({ many }) => ({
  generations: many(imageGenerations),
}));

export const editorMultisRelations = relations(editorMultis, ({ one, many }) => ({
  user: one(users, {
    fields: [editorMultis.userId],
    references: [users.id],
  }),
  editorMultiImages: many(editorMultiImages),
}));

export const editorMultiImagesRelations = relations(editorMultiImages, ({ one }) => ({
  user: one(users, {
    fields: [editorMultiImages.userId],
    references: [users.id],
  }),
  editor: one(editorMultis, {
    fields: [editorMultiImages.editorId],
    references: [editorMultis.id],
  }),
}));

// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type VerificationToken = typeof verificationTokens.$inferSelect;
export type NewVerificationToken = typeof verificationTokens.$inferInsert;
export type Authenticator = typeof authenticators.$inferSelect;
export type NewAuthenticator = typeof authenticators.$inferInsert;
export type Subscription = typeof subscriptions.$inferSelect;
export type NewSubscription = typeof subscriptions.$inferInsert;
export type Model = typeof models.$inferSelect;
export type NewModel = typeof models.$inferInsert;
export type ImageGeneration = typeof imageGenerations.$inferSelect;
export type NewImageGeneration = typeof imageGenerations.$inferInsert;
export type Image = typeof images.$inferSelect;
export type NewImage = typeof images.$inferInsert;
export type EditorMulti = typeof editorMultis.$inferSelect;
export type NewEditorMulti = typeof editorMultis.$inferInsert;
export type EditorMultiImage = typeof editorMultiImages.$inferSelect;
export type NewEditorMultiImage = typeof editorMultiImages.$inferInsert;
export type Debug = typeof debugs.$inferSelect;
export type NewDebug = typeof debugs.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;