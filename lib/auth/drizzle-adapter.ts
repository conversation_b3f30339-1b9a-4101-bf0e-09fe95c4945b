import { and, eq } from "drizzle-orm";
import type { Adapter } from "next-auth/adapters";
import { db } from "@/lib/db";
import {
  users,
  accounts,
  sessions,
  verificationTokens,
  type User,
  type Account,
  type Session,
  type VerificationToken,
} from "@/lib/db/schema";

export function DrizzleAdapter(): Adapter {
  return {
    async createUser(data) {
      const [user] = await db
        .insert(users)
        .values({
          id: crypto.randomUUID(),
          email: data.email,
          name: data.name,
          image: data.image,
          emailVerified: data.emailVerified,
        })
        .returning();

      return user;
    },
    async getUser(data) {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, data));

      return user ?? null;
    },
    async getUserByEmail(data) {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.email, data));

      return user ?? null;
    },
    async createSession(data) {
      const [session] = await db
        .insert(sessions)
        .values(data)
        .returning();

      return session;
    },
    async getSessionAndUser(data) {
      const [result] = await db
        .select({
          session: sessions,
          user: users,
        })
        .from(sessions)
        .where(eq(sessions.sessionToken, data))
        .innerJoin(users, eq(users.id, sessions.userId));

      return result ?? null;
    },
    async updateUser(data) {
      if (!data.id) {
        throw new Error("No user id.");
      }

      const [user] = await db
        .update(users)
        .set(data)
        .where(eq(users.id, data.id))
        .returning();

      return user;
    },
    async updateSession(data) {
      const [session] = await db
        .update(sessions)
        .set(data)
        .where(eq(sessions.sessionToken, data.sessionToken))
        .returning();

      return session;
    },
    async linkAccount(data) {
      await db.insert(accounts).values(data);
    },
    async getUserByAccount(data) {
      const [result] = await db
        .select({
          user: users,
        })
        .from(accounts)
        .where(
          and(
            eq(accounts.providerAccountId, data.providerAccountId),
            eq(accounts.provider, data.provider)
          )
        )
        .innerJoin(users, eq(accounts.userId, users.id));

      return result?.user ?? null;
    },
    async deleteSession(data) {
      const [session] = await db
        .delete(sessions)
        .where(eq(sessions.sessionToken, data))
        .returning();

      return session ?? null;
    },
    async createVerificationToken(data) {
      const [verificationToken] = await db
        .insert(verificationTokens)
        .values(data)
        .returning();

      return verificationToken;
    },
    async useVerificationToken(data) {
      try {
        const [verificationToken] = await db
          .delete(verificationTokens)
          .where(
            and(
              eq(verificationTokens.identifier, data.identifier),
              eq(verificationTokens.token, data.token)
            )
          )
          .returning();

        return verificationToken ?? null;
      } catch (err) {
        throw new Error("No verification token found.");
      }
    },
    async deleteUser(data) {
      const [user] = await db
        .delete(users)
        .where(eq(users.id, data))
        .returning();

      return user ?? null;
    },
    async unlinkAccount(data) {
      await db
        .delete(accounts)
        .where(
          and(
            eq(accounts.providerAccountId, data.providerAccountId),
            eq(accounts.provider, data.provider)
          )
        );
    },
  };
}
