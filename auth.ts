import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { db } from "./lib/db";
import { users, subscriptions } from "./lib/db/schema";
import { eq } from "drizzle-orm";
import { compare } from "bcryptjs";
import Google from "next-auth/providers/google";
import { verifyTurnstile } from "./lib/cf/turnstile";
import { redirect } from "next/navigation";
import { sendEmail } from "./lib/email/ses";
import { createVerifyEmail } from "./lib/email/verify";
import { DrizzleAdapter } from "./lib/auth/drizzle-adapter";

async function createDefaultSubscription(userId: string) {
    await db.insert(subscriptions).values({
        userId: userId,
        type: "FREE",
        status: "ACTIVE",
        creditsGrantedPerMonth: 30,
        creditsRemaining: 30,
    });
}

export const { handlers, auth, signIn, signOut} = NextAuth({
    adapter: DrizzleAdapter(),
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                token: { label: "Token", type: "hidden" },
            },
            async authorize(credentials) {

                if (!credentials?.email || !credentials?.password || !credentials?.token) {
                    return null
                }
                //check turnstile token
                
                try{
                    const isTurnstileValid = await verifyTurnstile(credentials?.token as string)
                    if (!isTurnstileValid) {
                        return null
                    }
                    const [user] = await db
                        .select()
                        .from(users)
                        .where(eq(users.email, credentials?.email as string));
                    if (!user) {
                        return null
                    }
                    const isPasswordValid = await compare(credentials?.password as string, user.passwordHash as string);
                    if (!isPasswordValid) {
                        return null
                    }
                    return user;
                }catch(error){
                    return null
                }
            },
        }),
        Google({
            clientId: process.env.AUTH_GOOGLE_ID,
            clientSecret: process.env.AUTH_GOOGLE_SECRET,
        })
    ],
    callbacks:{
        signIn: async ({user, account, profile, credentials, email}) => {
            try {
                //check user exist
                let userinfo;
                const [userWithSubscription] = await db
                    .select({
                        user: users,
                        subscription: subscriptions,
                    })
                    .from(users)
                    .leftJoin(subscriptions, eq(users.id, subscriptions.userId))
                    .where(eq(users.email, user.email as string));

                userinfo = userWithSubscription ? {
                    ...userWithSubscription.user,
                    subscription: userWithSubscription.subscription
                } : null;
                //邮箱注册用户首次登陆，创建默认订阅
                if(userinfo && !userinfo.subscription){
                    await createDefaultSubscription(userinfo.id)
                }
                //google注册用户首次登陆， 创建用户和 默认订阅
                if(!userinfo){
                    //create user
                    const [newUser] = await db.insert(users).values({
                        email: user.email as string,
                        name: profile?.name as string,
                        image: profile?.image as string,
                        emailVerified: null
                    }).returning();

                    userinfo = { ...newUser, subscription: null };

                    await createDefaultSubscription(newUser.id)
                }
               
                if (!userinfo?.emailVerified) {
                    console.log("user not verified")

                    //const verifyEmail = await createVerifyEmail(user.email as string);
                    //await sendEmail(user.email as string, 'Verify your email', verifyEmail);

                    //return '/pending-verification';
                }
                return true;
            } catch (error) {
                console.log("error in signIn");
                return false; // Explicitly return false on error to prevent login
            }
        },
        //end
        jwt: async ({ token, user }) => {
            if (user) {
                //find user by email    
                const userinfo = await prisma.user.findUnique({
                    where: {
                        email: user.email as string,
                    },
                });
                token.id = userinfo?.id as string; 
                token.email = user.email as string;
                token.name = user.name as string;
                token.image = user.image as string;
            }
            return token;
        },
        session: async ({ session, token }) => { 
            if (session.user && token.id) { 
                session.user.id = token.id as string; 
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.image = token.image as string;
                
                // 获取用户积分和模型信息
                const userWithData = await prisma.user.findUnique({
                    where: {
                        id: token.id as string,
                    },
                    include: {
                        subscription: true,
                    }
                });
                
                if (userWithData && userWithData.subscription) {
                    session.user.credits = userWithData.subscription.creditsRemaining;
                }
                
            }
            return session;
        },
    }
})